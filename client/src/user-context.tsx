import { StreamVideoClient } from "@stream-io/video-react-sdk";
import { createContext, useState } from "react";
import { useContext } from "react";
interface User {
  username: string;
  name: string;
}
interface UserContextProps {
  user: User | null;
  setUser: (user: User | null) => void;
  client: StreamVideoClient | undefined;
  setClient: (client: StreamVideoClient | undefined) => void;
}

const UserContext = createContext<UserContextProps | undefined>(undefined);

interface userProviderProps {
  children: React.ReactNode;
}

export const UserProvider = (props: userProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [client, setClient] = useState<StreamVideoClient>();
  return (
    <UserContext.Provider value={{ user, setUser, client, setClient }}>
      {" "}
      {props.children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
